
@import url('https://fonts.googleapis.com/css2?family=Marcellus&family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700&display=swap');

/* Leaflet CSS for map functionality */
@import 'leaflet/dist/leaflet.css';

/* Custom styles for map clusters */
.custom-marker-cluster {
  background: transparent;
  border: none;
}

.cluster-marker {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid white;
}

.cluster-small {
  width: 30px;
  height: 30px;
  background-color: #B9A590; /* taupe color */
  font-size: 12px;
}

.cluster-medium {
  width: 40px;
  height: 40px;
  background-color: #574C3F; /* brown color */
  font-size: 14px;
}

.cluster-large {
  width: 50px;
  height: 50px;
  background-color: #36302A; /* brown-dark color */
  font-size: 16px;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-body bg-beige-light dark:bg-brown text-brown-dark dark:text-beige-light;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  }

  h2 {
    @apply text-3xl md:text-4xl font-bold;
  }

  h3 {
    @apply text-2xl md:text-3xl font-semibold;
  }

  h4 {
    @apply text-xl md:text-2xl font-semibold;
  }

  h5 {
    @apply text-lg md:text-xl font-medium;
  }

  h6 {
    @apply text-base md:text-lg font-medium;
  }

  p {
    @apply text-base leading-relaxed;
  }

  a {
    @apply text-taupe hover:text-brown dark:hover:text-beige-medium transition-colors duration-300;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-md font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-taupe text-white hover:bg-brown focus:ring-taupe;
  }

  .btn-secondary {
    @apply btn bg-brown-dark text-white hover:bg-brown focus:ring-brown-dark;
  }

  .btn-outline {
    @apply btn border-2 border-taupe text-taupe hover:bg-taupe hover:text-white focus:ring-taupe;
  }

  .card {
    @apply bg-white dark:bg-brown-dark rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg;
  }

  .input {
    @apply w-full px-4 py-2 border border-taupe rounded-md focus:outline-none focus:ring-2 focus:ring-taupe bg-white dark:bg-brown-dark text-brown-dark dark:text-beige-light;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  color-scheme: light dark;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: transparent;
  transition: background-color 0.5s ease, color 0.5s ease;
}

svg {
  cursor: pointer;
  transition: 300ms ease-in-out;
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
