import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Heart, Search, Home, MessageCircle, User, BarChart3, Clock, AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "../../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../../ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../../ui/tabs";
import { Skeleton } from "../../ui/skeleton";
import { Badge } from "../../ui/badge";
import { favoritesService } from "../../../lib/favoritesService";
import { useAuth } from "../../../contexts/AuthContext";
import PropertyCard from "../../UI/PropertyCard";
import RecentProperties from "./RecentProperties";
import ChatInterface from "../Chat/ChatInterface";

/**
 * Client Dashboard component for displaying user's favorite properties, recent properties, and chat
 */
const ClientDashboard = () => {
  const { user } = useAuth();
  const [favoriteProperties, setFavoriteProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [favoritesCount, setFavoritesCount] = useState(0);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch user's favorite properties on component mount
  useEffect(() => {
    const fetchFavorites = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Fetch user's favorite properties
        const favoritesResult = await favoritesService.getUserFavorites();
        console.log('Favorites result:', favoritesResult);

        if (favoritesResult.error) {
          console.error('Favorites error:', favoritesResult.error);
          throw favoritesResult.error;
        }

        // Fetch favorites count
        const countResult = await favoritesService.getFavoritesCount();
        console.log('Count result:', countResult);

        if (countResult.error) {
          console.error('Count error:', countResult.error);
          throw countResult.error;
        }

        setFavoriteProperties(favoritesResult.data || []);
        setFavoritesCount(countResult.data || 0);
      } catch (err) {
        console.error("Error fetching favorites:", err);
        setError("Failed to load your favorite properties. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchFavorites();
  }, [user]);

  // Refresh favorites when a property is favorited/unfavorited
  const refreshFavorites = async () => {
    if (!user) return;

    try {
      const favoritesResult = await favoritesService.getUserFavorites();
      if (favoritesResult.error) throw favoritesResult.error;

      const countResult = await favoritesService.getFavoritesCount();
      if (countResult.error) throw countResult.error;

      setFavoriteProperties(favoritesResult.data || []);
      setFavoritesCount(countResult.data || 0);
    } catch (err) {
      console.error("Error refreshing favorites:", err);
    }
  };

  // Handle property card favorite toggle callback
  const handleFavoriteToggle = () => {
    // Refresh the favorites list when a property is favorited/unfavorited
    refreshFavorites();
  };

  // Expose test function to window for debugging
  useEffect(() => {
    window.testFavorites = async () => {
      console.log('=== MANUAL FAVORITES TEST ===');
      const result = await favoritesService.testAddFavorite();
      console.log('Manual test result:', result);
      return result;
    };

    return () => {
      delete window.testFavorites;
    };
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardContent className="p-6">
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Show authentication required message if user is not logged in
  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card className="max-w-md mx-auto">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2">
              Sign In to View Your Dashboard
            </h2>
            <p className="text-muted-foreground mb-6">
              Create an account or sign in to save your favorite properties and access your personalized dashboard.
            </p>
            <Button asChild className="bg-taupe hover:bg-brown">
              <Link to="/auth">
                Sign In / Sign Up
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold mb-2">
            Client Dashboard
          </h1>
          <p className="text-muted-foreground">
            Manage your favorite properties, view recent listings, and chat with our team
          </p>
        </div>
        <Button asChild className="bg-taupe hover:bg-brown">
          <Link to="/properties">
            <Search className="h-4 w-4 mr-2" />
            Browse Properties
          </Link>
        </Button>
      </div>

      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="favorites" className="flex items-center gap-2">
            <Heart className="h-4 w-4" />
            <span className="hidden sm:inline">Favorites</span>
            <Badge variant="secondary" className="ml-1">
              {favoritesCount}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            <span className="hidden sm:inline">Chat Support</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Recent Properties Section */}
          <RecentProperties />

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <Heart className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{favoritesCount}</p>
                    <p className="text-muted-foreground">Favorite Properties</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">4</p>
                    <p className="text-muted-foreground">Recent Properties</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <MessageCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">24/7</p>
                    <p className="text-muted-foreground">Chat Support</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="favorites" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              My Favorite Properties
            </h2>
            <p className="text-muted-foreground">
              {favoritesCount === 0
                ? "You haven't saved any properties yet"
                : `You have ${favoritesCount} favorite ${favoritesCount === 1 ? 'property' : 'properties'}`
              }
            </p>
          </div>

          {error && (
            <div className="bg-destructive/15 border border-destructive/20 text-destructive px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Favorite Properties Grid */}
          {favoriteProperties.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {favoriteProperties.map((property) => (
                <PropertyCard
                  key={property.id}
                  property={property}
                  onFavoriteToggle={handleFavoriteToggle}
                  showFavoriteButton={true}
                  linkTo={`/client/properties/${property.id}`}
                />
              ))}
            </div>
          ) : (
            <Card className="max-w-md mx-auto">
              <CardContent className="p-12 text-center">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  No Favorite Properties Yet
                </h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  Start exploring our property listings and save your favorites by clicking the heart icon on any property card.
                </p>
                <Button asChild className="bg-taupe hover:bg-brown">
                  <Link to="/properties">
                    <Home className="h-4 w-4 mr-2" />
                    Explore Properties
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="chat" className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              Chat Support
            </h2>
            <p className="text-muted-foreground">
              Get instant help from our real estate experts
            </p>
          </div>
          <ChatInterface />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ClientDashboard;
