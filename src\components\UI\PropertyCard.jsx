/* eslint-disable react/prop-types */
import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Heart, MapPin, Bed, Bath, Square, Eye, Map, Loader2 } from "lucide-react";
import { Card, CardContent } from "./card";
import { But<PERSON> } from "./button";
import { Badge } from "./badge";
import { formatPropertyPrice } from "../../utils/currencyUtils";
import { favoritesService } from "../../lib/favoritesService";
import { useAuth } from "../../contexts/AuthContext";
import { cn } from "../../lib/utils";

const PropertyCard = ({ property, onFavoriteToggle }) => {
  const { user } = useAuth();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check if property is favorited when component mounts or user changes
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      if (user && property.id) {
        const { data } = await favoritesService.isPropertyFavorited(property.id);
        setIsFavorite(data);
      } else {
        setIsFavorite(false);
      }
    };

    checkFavoriteStatus();
  }, [user, property.id]);

  const toggleFavorite = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('Heart icon clicked for property:', property.id);
    console.log('Current favorite status:', isFavorite);
    console.log('Current user:', user?.id);

    // Require authentication for favorites
    if (!user) {
      alert('Please sign in to add properties to your favorites');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Calling toggleFavorite service...');
      const result = await favoritesService.toggleFavorite(property.id, isFavorite);
      console.log('Toggle favorite result:', result);

      if (result.error) {
        console.error('Error toggling favorite:', result.error);
        alert('Failed to update favorites. Please try again.');
      } else {
        console.log('Successfully toggled favorite, updating UI state');
        setIsFavorite(!isFavorite);

        // Call the callback if provided
        if (onFavoriteToggle) {
          onFavoriteToggle();
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      alert('Failed to update favorites. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get the first image URL
  const getImageUrl = () => {
    // Handle different image URL field names
    if (property.thumbnail_url) return property.thumbnail_url;
    if (property.imageUrl) return property.imageUrl;
    if (property.images && property.images.length > 0) {
      return property.images[0].url || property.images[0].image_url;
    }
    if (property.property_images && property.property_images.length > 0) {
      return property.property_images[0].image_url || property.property_images[0].url;
    }
    return "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";
  };

  // Helper function to clean string values (remove extra quotes)
  const cleanString = (str) => {
    if (!str) return "";
    return str.toString().replace(/^"|"$/g, '');
  };



  // Helper function to safely format square feet
  const formatSquareFeet = () => {
    const sqft = property.sqft || property.square_feet;
    if (sqft && !isNaN(sqft)) {
      return sqft.toLocaleString();
    }
    return "N/A";
  };

  return (
    <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300 border-border/50 hover:border-border">
      {/* Image Container */}
      <div className="relative overflow-hidden h-48 xs:h-56 sm:h-64">
        <img
          src={getImageUrl()}
          alt={property.title || "Property"}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";
          }}
        />

        {/* Price Badge */}
        <Badge className="absolute top-3 left-3 bg-background/95 text-foreground border-border/50 font-semibold shadow-md">
          {formatPropertyPrice(property.price)}
          {(property.isRental || property.sale_type === "For Rent") && (
            <span className="text-xs font-normal">/mo</span>
          )}
        </Badge>

        {/* Favorite Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleFavorite}
          disabled={isLoading}
          className={cn(
            "absolute top-3 right-3 h-8 w-8 bg-background/95 hover:bg-background border-border/50 shadow-md",
            isLoading && "opacity-50 cursor-not-allowed"
          )}
          aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Heart
              className={cn(
                "h-4 w-4 transition-colors",
                isFavorite ? "fill-red-500 text-red-500" : "text-muted-foreground hover:text-red-500"
              )}
            />
          )}
        </Button>

        {/* Property Type Badge */}
        {(property.type || property.property_type) && (
          <Badge className="absolute bottom-3 left-3 bg-taupe text-white border-taupe">
            {cleanString(property.type || property.property_type)}
          </Badge>
        )}
      </div>

      {/* Content */}
      <CardContent className="p-4">
        <h3 className="font-heading font-bold text-lg mb-2 text-foreground line-clamp-1">
          {cleanString(property.title) || "Property"}
        </h3>

        {/* Location */}
        <div className="flex items-center mb-3 text-muted-foreground">
          <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
          <span className="text-sm truncate">
            {cleanString(property.location) || "Location not specified"}
          </span>
        </div>

        {/* Features */}
        <div className="flex justify-between mb-4 text-muted-foreground">
          <div className="flex items-center">
            <Bed className="h-4 w-4 mr-1" />
            <span className="text-sm">{property.bedrooms || 0} Beds</span>
          </div>
          <div className="flex items-center">
            <Bath className="h-4 w-4 mr-1" />
            <span className="text-sm">{property.bathrooms || 0} Baths</span>
          </div>
          <div className="flex items-center">
            <Square className="h-4 w-4 mr-1" />
            <span className="text-sm">{formatSquareFeet()} sqft</span>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm" asChild className="border-taupe text-taupe hover:bg-taupe hover:text-white">
            <Link to={`/properties/${property.id}`}>
              <Eye className="h-4 w-4 mr-1" />
              View Details
            </Link>
          </Button>
          {property.latitude && property.longitude && (
            <Button variant="outline" size="sm" asChild className="border-brown text-brown hover:bg-brown hover:text-white">
              <Link to={`/properties?lat=${property.latitude}&lng=${property.longitude}&zoom=15&property=${property.id}`}>
                <Map className="h-4 w-4 mr-1" />
                View on Map
              </Link>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PropertyCard;
