import { motion } from "framer-motion";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import SectionHeading from "../UI/SectionHeading";

const steps = [
  {
    number: "01",
    title: "Discover Properties",
    description:
      "Browse our curated selection of premium properties or use our advanced search to find exactly what you're looking for.",
    image:
      "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80",
  },
  {
    number: "02",
    title: "Connect with an Agent",
    description:
      "Our experienced agents will guide you through the process, arrange viewings, and provide expert advice tailored to your needs.",
    image:
      "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80",
  },
  {
    number: "03",
    title: "Close the Deal",
    description:
      "From offer to closing, we handle the negotiations, paperwork, and logistics to ensure a smooth and successful transaction.",
    image:
      "https://images.unsplash.com/photo-1589939705384-5185137a7f0f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
  },
];

const HowItWorks = () => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <SectionHeading
          title="How It Works"
          subtitle="Our streamlined process makes finding and securing your dream property simple and stress-free."
          centered
        />

        <div className="mt-12 space-y-16 md:space-y-24">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`flex flex-col ${
                index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
              } items-center gap-8 md:gap-12`}
            >
              {/* Image */}
              <motion.div
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6 }}
                className="w-full md:w-1/2"
              >
                <div className="relative">
                  <img
                    src={step.image}
                    alt={step.title}
                    className="w-full h-[300px] md:h-[400px] object-cover rounded-xl shadow-xl"
                  />

                  <Badge className="absolute -top-3 -left-3 bg-gradient-to-br from-taupe to-brown text-white text-2xl md:text-3xl font-bold font-heading w-14 h-14 md:w-16 md:h-16 flex items-center justify-center rounded-full shadow-lg border-4 border-background">
                    {step.number}
                  </Badge>
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: index % 2 === 0 ? 50 : -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6 }}
                className="w-full md:w-1/2"
              >
                <h3 className="text-2xl md:text-3xl font-heading font-bold mb-4 text-foreground">
                  {step.title}
                </h3>
                <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                  {step.description}
                </p>
                {index === 0 && (
                  <Button asChild size="lg" className="bg-taupe hover:bg-brown text-white">
                    <a href="/properties">
                      Browse Properties
                    </a>
                  </Button>
                )}
                {index === 1 && (
                  <Button asChild size="lg" className="bg-taupe hover:bg-brown text-white">
                    <a href="/about#team">
                      Meet Our Agents
                    </a>
                  </Button>
                )}
                {index === 2 && (
                  <Button asChild size="lg" className="bg-taupe hover:bg-brown text-white">
                    <a href="/contact">
                      Get Started
                    </a>
                  </Button>
                )}
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
