/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class", "class"],
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}",
    "./node_modules/@material-tailwind/react/components/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@material-tailwind/react/theme/components/**/*.{js,ts,jsx,tsx}",
    "./src/components/ui/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			beige: {
  				light: '#F6F3EC',
  				medium: '#ECE4DA'
  			},
  			taupe: '#B9A590',
  			brown: {
  				DEFAULT: '#574C3F',
  				dark: '#36302A'
  			},
  			white: '#FFFFFF',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			'card-foreground': '#36302A',
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			'popover-foreground': '#36302A',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			'primary-foreground': '#F6F3EC',
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			'secondary-foreground': '#F6F3EC',
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			'muted-foreground': '#574C3F',
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			'accent-foreground': '#F6F3EC',
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			'destructive-foreground': '#F6F3EC',
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		fontFamily: {
  			heading: [
  				'MARCELLUS',
  				'serif'
  			],
  			body: [
  				'PT SERIF',
  				'serif'
  			]
  		},
  		container: {
  			center: true,
  			padding: {
  				DEFAULT: '0.5rem',
  				xs: '1rem',
  				sm: '2rem',
  				lg: '4rem',
  				xl: '5rem'
  			},
  			screens: {
  				xs: '400px',
  				ss: '600px',
  				sm: '800px',
  				md: '1000px',
  				lg: '1200px',
  				xl: '1700px'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	},
  	screens: {
  		xs: '400px',
  		ss: '600px',
  		sm: '800px',
  		md: '1000px',
  		lg: '1200px',
  		xl: '1700px'
  	}
  },
  plugins: [require("tailwindcss-animate")], // Node.js require function
};
