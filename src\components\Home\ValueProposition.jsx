import { motion } from "framer-motion";
import { Shield, Home, Clock, Users } from "lucide-react";
import { Card, CardContent } from "../ui/card";
import SectionHeading from "../UI/SectionHeading";

const features = [
  {
    icon: Shield,
    title: "Trusted Expertise",
    description:
      "With over 20 years of experience, our team of professionals provides unmatched real estate knowledge and personalized service.",
  },
  {
    icon: Home,
    title: "Premium Properties",
    description:
      "Access to exclusive listings in the most desirable locations, from luxury homes to high-potential investment opportunities.",
  },
  {
    icon: Clock,
    title: "Streamlined Process",
    description:
      "We handle every detail from search to closing, making your real estate journey smooth, efficient, and stress-free.",
  },
  {
    icon: Users,
    title: "Dedicated Support",
    description:
      "Our client-first approach means you'll have a dedicated agent available to answer questions and provide guidance at every step.",
  },
];

const ValueProposition = () => {
  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <SectionHeading
          title="Why Choose UrbanEdge"
          subtitle="We combine industry expertise with personalized service to deliver exceptional real estate experiences."
          centered
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full text-center hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-taupe to-brown rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-heading font-bold mb-3 text-foreground">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ValueProposition;
