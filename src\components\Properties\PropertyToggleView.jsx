import { Grid3X3, Map } from "lucide-react";
import { Button } from "../ui/button";

const PropertyToggleView = ({ view, onViewChange }) => {
  return (
    <div className="flex rounded-lg overflow-hidden border">
      <Button
        variant={view === "grid" ? "default" : "ghost"}
        onClick={() => onViewChange("grid")}
        className={`rounded-none ${view === "grid" ? "bg-taupe hover:bg-brown" : ""}`}
        aria-label="Grid view"
        aria-pressed={view === "grid"}
      >
        <Grid3X3 className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Grid</span>
      </Button>
      <Button
        variant={view === "map" ? "default" : "ghost"}
        onClick={() => onViewChange("map")}
        className={`rounded-none ${view === "map" ? "bg-taupe hover:bg-brown" : ""}`}
        aria-label="Map view"
        aria-pressed={view === "map"}
      >
        <Map className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Map</span>
      </Button>
    </div>
  );
};

export default PropertyToggleView;
